extends Control
class_name DialogueSystem

signal dialogue_finished
signal dialogue_advanced

@onready var dialogue_panel: PanelContainer = $DialoguePanel
@onready var speaker_label: Label = $DialoguePanel/VBoxContainer/SpeakerLabel
@onready var text_label: RichTextLabel = $DialoguePanel/VBoxContainer/TextLabel
@onready var continue_button: Button = $DialoguePanel/VBoxContainer/ContinueButton

var current_dialogue: Array[Dictionary] = []
var current_line_index: int = 0
var is_typing: bool = false
var typing_speed: float = 0.05

func _ready():
	hide()

	# Bezpečné pripojenie signálov
	if continue_button:
		continue_button.pressed.connect(_on_continue_pressed)

	# Nastavenie štýlu dialógového panelu
	if dialogue_panel:
		dialogue_panel.modulate = Color(1, 1, 1, 0.95)

func start_dialogue(dialogue_data: Array[Dictionary]):
	current_dialogue = dialogue_data
	current_line_index = 0
	show()
	display_current_line()

func display_current_line():
	if current_line_index >= current_dialogue.size():
		end_dialogue()
		return

	var line_data = current_dialogue[current_line_index]
	if speaker_label:
		speaker_label.text = line_data.get("speaker", "Rozprávač")

	# Animácia písania textu
	if text_label:
		text_label.text = ""
	is_typing = true
	if continue_button:
		continue_button.disabled = true

	var full_text = line_data.get("text", "")
	type_text(full_text)

func type_text(text: String):
	if text_label:
		text_label.text = ""

	for i in range(text.length()):
		if not is_typing:
			break
		if text_label:
			text_label.text += text[i]
		await get_tree().create_timer(typing_speed).timeout

	is_typing = false
	if continue_button:
		continue_button.disabled = false
		continue_button.grab_focus()

func _on_continue_pressed():
	if is_typing:
		# Preskočiť animáciu písania
		is_typing = false
		if text_label and current_line_index < current_dialogue.size():
			text_label.text = current_dialogue[current_line_index].get("text", "")
		if continue_button:
			continue_button.disabled = false
		return

	current_line_index += 1
	dialogue_advanced.emit()
	display_current_line()

func end_dialogue():
	hide()
	dialogue_finished.emit()

func _input(event):
	if visible and event.is_action_pressed("ui_accept"):
		_on_continue_pressed()
	elif visible and event.is_action_pressed("ui_cancel"):
		skip_dialogue()

func skip_dialogue():
	is_typing = false
	end_dialogue()

# Prednastavené dialógy pre rozprávača
# Funkcie pre získanie dialógov podľa fázy príbehu
func get_chapter_intro_dialogue(chapter_number: int) -> Array[Dictionary]:
	if chapter_number == 1:
		return [
			{"speaker": "Rozprávač", "text": "Marec 1894. Studený dážď bičuje okná kočiara kolísajúceho sa na úzkej ceste cez karpatské horstvo."},
			{"speaker": "Rozprávač", "text": "Búrka silnie s každou míľou, ktorou sa približujete k zámku Van Helsinga."},
			{"speaker": "Rozprávač", "text": "V kočiari sedíte už štyri hodiny, študujúc posledné poznámky svojho mentora."},
			{"speaker": "Rozprávač", "text": "Vzduch je napätý – nielen kvôli blížiacej sa búrke, ale aj preto, že cítite hrozbu niečoho omnoho nebezpečnejšieho."},
			{"speaker": "Rozprávač", "text": "Van Helsingov posledný telegram bol nezvyčajne stručný a naliehavý. To nie je jeho štýl – určite sa deje niečo vážne."},
			{"speaker": "Rozprávač", "text": "Vonku zúri živelný chaos: vietor vyje v korunách stromov ako tisíc duší v mukách."},
			{"speaker": "Rozprávač", "text": "Každý blesk na okamih osvetlí zahmlenú krajinu, akoby samotná príroda odmietala odhaliť svoje tajomstvá."},
			{"speaker": "Rozprávač", "text": "Kočiš je nervózny – už hodinu neprehovoril ani slovo."},
			{"speaker": "Rozprávač", "text": "Van Helsingov list obsahuje zašifrovanú správu. Jeho poznámka znie: 'Pamätaj - každé písmeno posuniem o jedno miesto dopredu.'"}
		]
	elif chapter_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Stojíte pred masívnou železnou bránou zdobenou heraldickými symbolmi."},
			{"speaker": "Rozprávač", "text": "Každý erb rozpráva príbeh – moc, pád dynastie, vzostup i úpadok."},
			{"speaker": "Rozprávač", "text": "Brána je zamknutá, v oknách nevidno svetlo. Zámok pôsobí opustene."},
			{"speaker": "Rozprávač", "text": "Havrany krákajú zo strešných ríms, akoby sa vysmievali vašej situácii."},
			{"speaker": "Rozprávač", "text": "Na bráne je krvou napísaný text: 'MOJE MENO JE SMRŤ, ALE POZADU SOM ŽIVOT'"}
		]
	elif chapter_number == 3:
		return [
			{"speaker": "Rozprávač", "text": "Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu."},
			{"speaker": "Rozprávač", "text": "Steny zdobí zbierka starodávnych zbraní – meče, kopije, kuše."},
			{"speaker": "Rozprávač", "text": "V rohu tikajú staré hodiny; ich monotónne klikanie je jediné, čo narúša ticho."},
			{"speaker": "Viktor", "text": "Pán doktor odišiel včera večer krátko po západe slnka."},
			{"speaker": "Viktor", "text": "Povedal iba: 'Viktor, idem preskúmať staré krídlo zámku. Ak sa do úsvitu nevrátim, pošli telegram Rádu.'"},
			{"speaker": "Viktor", "text": "Odvtedy o ňom niet správy."},
			{"speaker": "Viktor", "text": "Do toho krídla nik nevošiel celé desaťročia. Je to najstaršia časť zámku."}
		]
	elif chapter_number == 4:
		return [
			{"speaker": "Rozprávač", "text": "Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší."},
			{"speaker": "Rozprávač", "text": "Steny pokrýva vlhkosť a medzi kameňmi rastú čudné plesne."},
			{"speaker": "Rozprávač", "text": "Občas počuť mechanické cvakanie – v múroch stále pracujú skryté mechanizmy."},
			{"speaker": "Viktor", "text": "Počkajte! Spomínam si na básničku."},
			{"speaker": "Viktor", "text": "'Kráčaj, kde Mesiac svieti, nie tam, kde Slnko horí.' V núdzi ju vraj použijem."}
		]
	elif chapter_number == 5:
		return [
			{"speaker": "Rozprávač", "text": "Za laboratóriom sa skrýva úzke kamenné schodisko vedúce do hlbín."},
			{"speaker": "Rozprávač", "text": "Kamene sú ošúchané tisíckami krokov, no roky tu nik nebol."},
			{"speaker": "Rozprávač", "text": "Z hĺbky sála chlad a vlhkosť, pripomínajúce dych hrobky."},
			{"speaker": "Viktor", "text": "Počkám tu a budem strážiť ústup!"},
			{"speaker": "Viktor", "text": "Ak sa niečo stane, kričte. A vezmite si toto..."},
			{"speaker": "Viktor", "text": "Kríž požehnal sám ostrihomský arcibiskup. Môže vám zachrániť život."}
		]

	return [{"speaker": "Rozprávač", "text": "Kapitola sa načítava..."}]

# Dialógy medzi hlavolamami
func get_interlude_dialogue(chapter_number: int, phase: int) -> Array[Dictionary]:
	if chapter_number == 1:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Srdce vám takmer zastane: 'Grófka je v krypte.'"},
				{"speaker": "Rozprávač", "text": "Znamená to, že Van Helsing našiel, čo hľadal – Isabelle Báthoryovú, poslednú z prekliatej línie."},
				{"speaker": "Rozprávač", "text": "Prečo však nepríde za vami? Prečo vás volá na zámok? A prečo je jeho list taký ustráchaný?"},
				{"speaker": "Rozprávač", "text": "Blesk, prudké zastavenie kočiara."},
				{"speaker": "Kočiš", "text": "To je všetko! Ďalej nejdem, páni!"},
				{"speaker": "Kočiš", "text": "Ani korunu by ste mi nedali, aby som šiel bližšie k tomu prekliatemu miestu!"},
				{"speaker": "Kočiš", "text": "Hovorí sa, že kto sa priblíži k zámku po západe slnka, už nikdy neuvidí úsvit."},
				{"speaker": "Kočiš", "text": "Musíte pokračovať pešo. Nech vás Boh ochraňuje..."},
				{"speaker": "Rozprávač", "text": "Kočiš vám hodí batožinu do blata a bez slova odcvála."},
				{"speaker": "Rozprávač", "text": "Jeho kočiar mizne v hukote búrky. Ostávate sami – uprostred karpatskej divočiny, s nocou, ktorá sa rýchlo blíži."},
				{"speaker": "Rozprávač", "text": "Vaše kroky sa zabárajú do hlbokého blata."},
				{"speaker": "Rozprávač", "text": "Les pôsobí ako živá bytosť – počuť šepot medzi konármi, praskot vetvičiek, vzdialené vytie, ktoré neznie celkom vlčie."},
				{"speaker": "Rozprávač", "text": "Hmla klesá a každý strom pripomína strážcu sledujúceho vaše pohyby."},
				{"speaker": "Rozprávač", "text": "Cesta sa rozdeľuje na štyri smery. Van Helsingove poznámky obsahujú zvláštnu básničku."},
				{"speaker": "Rozprávač", "text": "V denníku je poznámka: 'Východ, potom dvakrát na západ, nakoniec sever - tak sa dostaneš k zámku.'"}
			]
	elif chapter_number == 2:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Mechanizmus zaškrípe a brána sa pomaly otvára."},
				{"speaker": "Rozprávač", "text": "Vstupujete na nádvorie – dlažba je pokrytá machom a medzi kameňmi prerastá tráva."},
				{"speaker": "Rozprávač", "text": "Nádvorie je tiché ako hrob. Ticho prerušuje len kvapkanie vody zo žľabov."},
				{"speaker": "Rozprávač", "text": "Kdesi buchnú dvere – možno ich otvoril vietor, možno niečo iné."},
				{"speaker": "Viktor", "text": "Vy... ste to vy?"},
				{"speaker": "Viktor", "text": "Telegram odišiel pred troma dňami. Doktor vás čaká každú noc pri lampe."},
				{"speaker": "Viktor", "text": "Ale teraz nie je čas na spomienky."},
				{"speaker": "Viktor", "text": "Nie všetci, čo prichádzajú, patria medzi živých. Ak ste členovia Rádu, odpovedzte."},
				{"speaker": "Viktor", "text": "Tri otázky. Tri odpovede. Každá z iného sveta – kov, hviezda, bylina."}
			]
	elif chapter_number == 3:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Viktor", "text": "Vonkajšie múry! Samozrejme!"},
				{"speaker": "Viktor", "text": "Tam, kde sa najstaršie základy zámku spájajú s novou stavbou."},
				{"speaker": "Viktor", "text": "Viem presne, kde to je - pri severozápadnej veži."},
				{"speaker": "Rozprávač", "text": "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu."},
				{"speaker": "Rozprávač", "text": "Knihy sú v rôznych jazykoch – latinčina, nemčina, rumunčina, maďarčina."},
				{"speaker": "Rozprávač", "text": "Väčšina diel sa venuje tematikám, ktoré nie sú pre slabé povahy: démonológia, vampirológia, alchýmia."},
				{"speaker": "Rozprávač", "text": "Za tapisériou rodokmeňa odhaľujete tajnú priehradku."},
				{"speaker": "Rozprávač", "text": "Ukrytá je v nej drobná kožená knižka – osobný denník Van Helsinga."}
			]
	elif chapter_number == 4:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Úspech! Prešli ste bez spustenia mechanizmov."},
				{"speaker": "Rozprávač", "text": "Na konci chodby stoja masívne dubové dvere."},
				{"speaker": "Rozprávač", "text": "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami."},
				{"speaker": "Rozprávač", "text": "Stoly sú obsypané sklenenými nádobami, kotlíkmi a čudesnými prístrojmi."},
				{"speaker": "Viktor", "text": "Toto je recept na ochranný elixír!"},
				{"speaker": "Viktor", "text": "Doktor mi ho ukázal pred mesiacom."},
				{"speaker": "Viktor", "text": "Povedal: 'Viktor, ak ma pôjdeš hľadať do katakomb, priprav si tento elixír.'"}
			]
	elif chapter_number == 5:
		if phase == 1:  # Po prvom hlavolame
			return [
				{"speaker": "Rozprávač", "text": "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi."},
				{"speaker": "Rozprávač", "text": "V strede stojí kamenný podstavec. Rozhadzané ležia doktorove veci."},
				{"speaker": "Rozprávač", "text": "Rozbitá lampa, prázdny strieborný revolver, kožený notes s roztrhanými stranami."},
				{"speaker": "Rozprávač", "text": "Na kameňoch tmavnú škvrny, čo nápadne pripomínajú krv. No doktora niet."},
				{"speaker": "Van Helsing", "text": "Je tu! Isabelle má prisluhovačov – nie upírov, ale niečo horšie."},
				{"speaker": "Van Helsing", "text": "Ako vedela, že prídem? Musím sa dostať k jej sarkofágu..."},
				{"speaker": "Van Helsing", "text": "Voda dochádza, striebro tiež... sú ich príliš veľa..."},
				{"speaker": "Van Helsing", "text": "Ak toto niekto nájde, dokončite, čo som začal..."},
				{"speaker": "Rozprávač", "text": "Za podstavcom odkrývate tajné dvere vedúce do rozľahlej sály."},
				{"speaker": "Rozprávač", "text": "Uprostred stojí mohutný sarkofág zdobený tromi pákami."}
			]

	return []

func get_puzzle_intro_dialogue(chapter_number: int, puzzle_number: int) -> Array[Dictionary]:
	if chapter_number == 1:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Zašifrovaná správa: 'HSÔGLB KF X LSZQUF'"}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Musíte nájsť správnu cestu k zámku."}
			]
	elif chapter_number == 2:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Rozlúštite krvavý nápis na bráne."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Dokážte, že patríte k Rádu."}
			]
	elif chapter_number == 3:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stole nájdete Van Helsingov denník. Posledná stránka obsahuje zvláštnu správu."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "V denníku je poznámka o Isabelle Báthoryovej."}
			]
	elif chapter_number == 4:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "Na stene blikne krátka sekvencia 3 farieb. Musíte ju zopakovať."}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Na tabuli sú tri rovnice s vampírskymi symbolmi."}
			]
	elif chapter_number == 5:
		if puzzle_number == 1:
			return [
				{"speaker": "Rozprávač", "text": "V miestnosti sú 4 sviečky vrhajúce tiene s číslami: 7, 3, 9, 5."},
				{"speaker": "Rozprávač", "text": "Na podlahe je nápis: 'Súčet páru je tucet'"}
			]
		elif puzzle_number == 2:
			return [
				{"speaker": "Rozprávač", "text": "Na stene je vyškriabaný odkaz: 'Kráčaj, kde mesiac svieti, nie tam, kde slnko horí. Hviezda ti ukáže cestu.'"}
			]

	return [
		{"speaker": "Rozprávač", "text": "Pred vami sa objavuje nový hlavolam..."}
	]

func get_puzzle_success_dialogue(chapter_number: int = 0, puzzle_number: int = 0) -> Array[Dictionary]:
	if chapter_number == 1 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Konečne! Cez koruny stromov sa črtajú obrysy mohutných veží."},
			{"speaker": "Rozprávač", "text": "Zámok Van Helsinga sa týči pred vami ako čierna silueta proti búrlivej oblohe."},
			{"speaker": "Rozprávač", "text": "Táto stavba z 13. storočia v dnešnú noc pripomína skôr hrobku dávno mŕtvych kráľov než domov živého človeka."}
		]
	elif chapter_number == 2 and puzzle_number == 2:
		return [
			{"speaker": "Viktor", "text": "Tak predsa. Svetlo ešte úplne nezhaslo."},
			{"speaker": "Viktor", "text": "Poďte. A nech vás nočné tiene nespoznajú ako hostí."}
		]
	elif chapter_number == 3 and puzzle_number == 2:
		return [
			{"speaker": "Viktor", "text": "15. marca 1894 – Konečne!"},
			{"speaker": "Viktor", "text": "Po štyroch rokoch neúnavného pátrania som našiel dôkaz, že grófka Isabelle Báthoryová prežila svoju údajnú smrť."},
			{"speaker": "Viktor", "text": "Oficiálne záznamy sú prepracovaná lož."},
			{"speaker": "Viktor", "text": "Našiel som ju! Skrýva sa v kryptách pod týmto zámkom."},
			{"speaker": "Viktor", "text": "18. marca – Pripravujem sa na zostup do katakomb."},
			{"speaker": "Viktor", "text": "Potrebujem: strieborné gule, svätenú vodu z Ríma, dubový kôl, kríž môjho starého otca."},
			{"speaker": "Viktor", "text": "19. marca – Viktor netuší, kam idem. Tak je to lepšie."},
			{"speaker": "Viktor", "text": "Ak sa nevrátim do úsvitu, nech privolá Rád."},
			{"speaker": "Viktor", "text": "Krypty! Nik tam nevkročil celé storočia."},
			{"speaker": "Viktor", "text": "Vchod je zapečatený. Čo to pán doktor robil?"}
		]
	elif chapter_number == 4 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Elixír v fľaštičke jemne zažiari striebristým svetlom."},
			{"speaker": "Viktor", "text": "Výborne! Teraz sa môžeme odvážiť do katakomb."},
			{"speaker": "Viktor", "text": "Nezabudnite – elixír chráni len hodinu."}
		]
	elif chapter_number == 5 and puzzle_number == 2:
		return [
			{"speaker": "Rozprávač", "text": "Sarkofág sa pomaly otvára s hlbokým kamenným škrípotom."},
			{"speaker": "Rozprávač", "text": "Zvnútra sa ozýva slabý vzdych... alebo je to len vietor?"},
			{"speaker": "Rozprávač", "text": "Tajomstvo grófky Isabelle Báthoryovej je konečne odhalené."},
			{"speaker": "Rozprávač", "text": "Ale to je už príbeh pre ďalšiu kapitolu..."}
		]

	var success_messages = [
		[{"speaker": "Rozprávač", "text": "Výborne! Úspešne ste vyriešili hlavolam."}],
		[{"speaker": "Rozprávač", "text": "Bravó! Vaša logika vás neviedla."}],
		[{"speaker": "Rozprávač", "text": "Skvelé! Ďalšia záhada je odhalená."}]
	]
	return success_messages[randi() % success_messages.size()]

func get_puzzle_hint_dialogue(chapter_number: int = 0, puzzle_number: int = 0, hint_level: int = 1) -> Array[Dictionary]:
	if chapter_number == 1 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Skúste posunúť každé písmeno o jedno miesto dozadu v abecede."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Napríklad H sa stane G, S sa stane R."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Prvé slovo je GRÓFKA. Pokračujte ďalej."}]
	elif chapter_number == 1 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Sledujte presne poradie v poznámke."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Prvý smer je východ (V)."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Celé poradie: Východ, Západ, Západ, Sever."}]
	elif chapter_number == 2 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Čítaj pozorne - niekedy je odpoveď v opaku."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Smrť pozadu... čo dostaneš, keď otočíš slovo?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "SMRŤ odzadu je TRMS, ale nezabudni na piatu literu z 'SOM' → TRMSO"}]
	elif chapter_number == 2 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Viktor", "text": "Tri svety - kov, hviezda, bylina. Každý má svoju moc proti temnote."}]
			2:
				return [{"speaker": "Viktor", "text": "Kov čistí duše, hviezda ovláda vody, bylina chráni hroby."}]
			3:
				return [{"speaker": "Viktor", "text": "Striebro, Mesiac, Cesnak - základy každého lovca upírov."}]
	elif chapter_number == 3 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Van Helsing píše, že píše slová odzadu. Čo to znamená?"}]
			2:
				return [{"speaker": "Rozprávač", "text": "Skúste čítať každé slovo pozpätku."}]
			3:
				return [{"speaker": "Rozprávač", "text": "DOP = POD, IMÍŠJAKNOV = VONKAJŠÍMI..."}]
	elif chapter_number == 3 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Je to jednoduchá matematika."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Od roku sobáša odpočítajte vek."}]
			3:
				return [{"speaker": "Rozprávač", "text": "1596 - 20 = ?"}]
	elif chapter_number == 4 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Sústreďte sa a zapamätajte si poradie farieb."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Bola to sekvencia troch farieb."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Skúste znova a dávajte lepší pozor."}]
	elif chapter_number == 4 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Každý symbol skrýva číslo. Začnite s netopierom."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Ak dva netopiere dávajú 16, koľko je jeden netopier?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "Netopier=8, Krv=11, Rakva=7. Spočítajte ich."}]
	elif chapter_number == 5 and puzzle_number == 1:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Tiene nikdy neklamú, ale musíš ich správne spárovať."}]
			2:
				return [{"speaker": "Rozprávač", "text": "Tucet je 12. Ktoré dve čísla dávajú dokopy 12?"}]
			3:
				return [{"speaker": "Rozprávač", "text": "7+5=12 a 3+9=12. Zadaj všetky štyri čísla: 7539"}]
	elif chapter_number == 5 and puzzle_number == 2:
		match hint_level:
			1:
				return [{"speaker": "Rozprávač", "text": "Odkaz si pamätáš? 'Kde Mesiac svieti...'"}]
			2:
				return [{"speaker": "Rozprávač", "text": "Slnko horí - tam nechoď. Mesiac svieti - tam choď."}]
			3:
				return [{"speaker": "Rozprávač", "text": "Postupuj podľa básničky: najprv Mesiac, potom Hviezda."}]

	var hints = [
		[{"speaker": "Rozprávač", "text": "Možno by ste sa mali pozrieť na problém z iného uhla..."}],
		[{"speaker": "Rozprávač", "text": "Pamätajte, nie vždy je prvé riešenie to správne."}],
		[{"speaker": "Rozprávač", "text": "Skúste sa sústrediť na detaily, ktoré ste možno prehliadli."}]
	]
	return hints[randi() % hints.size()]
