extends Control
class_name ThreeSistersPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var statements_label: RichTextLabel = $PuzzlePanel/VBoxContainer/StatementsLabel
@onready var clue_label: Label = $PuzzlePanel/VBoxContainer/ClueLabel
@onready var maria_button: TextureButton = $PuzzlePanel/VBoxContainer/PortraitsContainer/MariaButton
@onready var anna_button: TextureButton = $PuzzlePanel/VBoxContainer/PortraitsContainer/AnnaButton
@onready var isabelle_button: TextureButton = $PuzzlePanel/VBoxContainer/PortraitsContainer/IsabelleButton
@onready var hint_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_answer: String = "maria"  # Mária hovorí pravdu
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if maria_button:
		maria_button.pressed.connect(_on_maria_pressed)
	if anna_button:
		anna_button.pressed.connect(_on_anna_pressed)
	if isabelle_button:
		isabelle_button.pressed.connect(_on_isabelle_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func show_puzzle():
	show()
	reset_puzzle()

func reset_puzzle():
	hint_level = 0
	reset_button_states()

func reset_button_states():
	# Nastavenie normálnych farieb
	if maria_button:
		maria_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if anna_button:
		anna_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if isabelle_button:
		isabelle_button.modulate = Color(1.0, 1.0, 1.0, 1.0)

func _on_maria_pressed():
	# Správna odpoveď
	maria_button.modulate = Color.GREEN
	show_success_feedback()
	await get_tree().create_timer(1.5).timeout
	puzzle_solved.emit()
	hide()

func _on_anna_pressed():
	# Nesprávna odpoveď
	anna_button.modulate = Color.RED
	show_error_feedback("Anna nie je správna odpoveď!")
	await get_tree().create_timer(2.0).timeout
	reset_button_states()

func _on_isabelle_pressed():
	# Nesprávna odpoveď
	isabelle_button.modulate = Color.RED
	show_error_feedback("Isabelle nie je správna odpoveď!")
	await get_tree().create_timer(2.0).timeout
	reset_button_states()

func show_success_feedback():
	# Zelené zablikanie správneho tlačidla
	var tween = create_tween()
	tween.tween_property(maria_button, "modulate", Color.WHITE, 0.3)
	tween.tween_property(maria_button, "modulate", Color.GREEN, 0.3)
	tween.tween_property(maria_button, "modulate", Color.WHITE, 0.3)
	tween.tween_property(maria_button, "modulate", Color.GREEN, 0.3)

func show_error_feedback(message: String):
	# Zobraz chybovú správu
	print("Chyba: ", message)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Ak len jedna hovorí pravdu, skúste postupne predpokladať, že pravdu hovorí každá z nich."
		2:
			return "Overte každú možnosť: vedie k logickému sporu alebo nie?"
		3:
			return "Ak Mária hovorí pravdu, Anna musí byť nevinná. Ale pozor - kto potom klame?"
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(text: String):
	# Jednoduchý dialóg s nápoveďou
	print("Nápoveda: ", text)
	# Tu by sa mohla zobraziť správa v UI
	await get_tree().create_timer(3.0).timeout

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
