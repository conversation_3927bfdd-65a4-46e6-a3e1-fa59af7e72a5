[res://scripts/Chapter.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 29,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://README.md]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "Markdown"
}

[res://scripts/CaesarCipherPuzzle.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 84,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/DialogueSystem.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 6,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/MemoryTestPuzzle.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 10,
"scroll_position": 10.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
